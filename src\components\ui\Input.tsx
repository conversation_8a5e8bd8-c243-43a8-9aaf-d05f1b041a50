import React, { useState, useId } from 'react';
import { cn } from '@/lib/utils';
import { InputProps } from '@/types';

const Input: React.FC<InputProps> = ({
  type = 'text',
  placeholder,
  value,
  onChange,
  disabled = false,
  error,
  label,
  required = false,
  className,
  size = 'md',
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const inputId = useId();
  const errorId = useId();

  const baseStyles = `
    block w-full rounded-lg border transition-all duration-200 ease-in-out
    placeholder:text-gray-400
    focus:outline-none focus:ring-2 focus:ring-offset-0
    disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed
    disabled:border-gray-200 disabled:shadow-none
  `;

  const sizeStyles = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-2.5 text-sm',
    lg: 'px-4 py-3 text-base',
  };

  const normalStyles = `
    border-gray-300 bg-white text-gray-900 shadow-sm
    hover:border-gray-400
    focus:border-primary-500 focus:ring-primary-500/20
  `;

  const errorStyles = `
    border-error-300 bg-error-50/50 text-error-900
    placeholder:text-error-400
    focus:border-error-500 focus:ring-error-500/20
  `;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (onChange) {
      onChange(e.target.value);
    }
  };

  const handleFocus = () => setIsFocused(true);
  const handleBlur = () => setIsFocused(false);

  return (
    <div className="w-full">
      {label && (
        <label
          htmlFor={inputId}
          className="block text-sm font-semibold text-gray-700 mb-2"
        >
          {label}
          {required && (
            <span className="text-error-500 ml-1" aria-label="required">
              *
            </span>
          )}
        </label>
      )}

      <div className="relative">
        <input
          id={inputId}
          type={type}
          placeholder={placeholder}
          value={value}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          disabled={disabled}
          aria-invalid={error ? 'true' : 'false'}
          aria-describedby={error ? errorId : undefined}
          className={cn(
            baseStyles,
            sizeStyles[size],
            error ? errorStyles : normalStyles,
            className
          )}
          {...props}
        />

        {/* Focus ring enhancement */}
        {isFocused && !disabled && (
          <div className={cn(
            'absolute inset-0 rounded-lg pointer-events-none',
            'ring-2 ring-offset-0',
            error ? 'ring-error-500/20' : 'ring-primary-500/20'
          )} />
        )}
      </div>

      {error && (
        <div className="mt-2 flex items-start gap-2">
          <svg
            className="w-4 h-4 text-error-500 mt-0.5 flex-shrink-0"
            fill="currentColor"
            viewBox="0 0 20 20"
            aria-hidden="true"
          >
            <path
              fillRule="evenodd"
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
              clipRule="evenodd"
            />
          </svg>
          <p id={errorId} className="text-sm text-error-600 leading-5">
            {error}
          </p>
        </div>
      )}
    </div>
  );
};

export default Input;
