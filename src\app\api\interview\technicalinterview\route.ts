// app/api/interview/technicalinterview/route.ts

import { NextRequest, NextResponse } from "next/server";

export async function POST(req: NextRequest) {
  try {
    // Step 1: Get data from frontend
    const body = await req.json();

    const {
      candidateid,
      roundName,
      interviewer,
      intervieweremail,
      scheduledAt,
      notes,
    } = body;

    if (
      !candidateid ||
      !roundName ||
      !interviewer ||
      !intervieweremail ||
      !scheduledAt
    ) {
      return NextResponse.json(
        { message: "Missing required fields" },
        { status: 400 }
      );
    }

    const createdBy = req.headers.get("X-Auth-User-Name");

    // Step 2: Send data to Node-RED
    const nodeRedRes = await fetch(`${process.env.NODE_RED_URL}/interviewroundsummary`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        candidateid,
        roundName,
        interviewer,
        intervieweremail,
        scheduledAt,
        createdBy,
      }),
    });

    if (!nodeRedRes.ok) {
      return NextResponse.json(
        { message: "Failed to forward data to Node-RED" },
        { status: 500 }
      );
    }

    const nodeRedResponse = await nodeRedRes.json();

    // Step 3: Respond back to frontend
    return NextResponse.json(nodeRedResponse);

  } catch (error) {
    console.error("Error:", error);
    return NextResponse.json({ message: "Internal Server Error" }, { status: 500 });
  }
}

// create put method for updating interview round summary
export async function PUT(req: NextRequest) {
    try {
        // Step 1: Validate request body
        const { candidateid, roundName, summary, decision } =
        await req.json();
            if (!candidateid || !roundName || !summary || !decision) {
            return NextResponse.json(
                { message: "Missing required fields" },
                { status: 400 }
            );
        }
        // Step 2: Send data to Node-RED
        const nodeRedRes = await fetch(`${process.env.NODE_RED_URL}/interviewroundsummary-technnical`,
            {
                method: "PUT",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({
                    candidateid,
                    roundName,
                   summary,
                    decision,
                }),
            }
        );

        if (!nodeRedRes.ok) {
            return NextResponse.json(
                { message: "Failed to forward data to Node-RED" },
                { status: 500 }
            );
        }   

        const nodeRedResponse = await nodeRedRes.json();

        // Step 3: Respond back to frontend
        return NextResponse.json(nodeRedResponse);

    } catch (error) {
        console.error("Error:", error);
        return NextResponse.json({ message: "Internal Server Error" }, { status: 500 });
    }
}

// to create get method for fetching interview round summary 
export async function GET(req: NextRequest) { 
    try {
        const { searchParams } = new URL(req.url);
        const candidateId = searchParams.get('candidate_id');
        const roundName = searchParams.get('roundName');
        const nodeRedRes = await fetch(`${process.env.NODE_RED_URL}/get-interviewroundsummary-technnical?candidateid=${candidateId}&roundName=${roundName}`, {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                    },
                    }
                    );
                    if (!nodeRedRes.ok) {
                        return NextResponse.json(
                            { message: "Failed to forward data to Node-RED" },
                            { status: 500 }
                            );
                            }

        const nodeRedResponse = await nodeRedRes.json();

        // Step 3: Respond back to frontend
        return NextResponse.json(nodeRedResponse);

    } catch (error) {
        console.error("Error:", error);
        return NextResponse.json({ message: "Internal Server Error" }, { status: 500 });
    }
}
