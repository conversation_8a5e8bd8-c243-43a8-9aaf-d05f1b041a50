'use client';

import React from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { Icon } from '@/components/ui';
import { NavigationItem } from '@/types';

interface SidebarProps {
  items: NavigationItem[];
  className?: string;
  onItemClick?: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ items, className, onItemClick }) => {
  const router = useRouter();
  const pathname = usePathname();

  const handleItemClick = (item: NavigationItem) => {
    console.log("navigation",item.href);
    if(item.href === '/api/auth/logout'){
      localStorage.clear();
       window.location.href = item.href; // ✅ Full redirect that Auth0 requires
        return;
    }else{
    router.push(item.href);
    }
    onItemClick?.(); // Close sidebar on mobile after navigation
  };

  const isActive = (href: string) => {
    // Handle exact match for dashboard
    if (href === '/dashboard' && pathname === '/dashboard') {
      return true;
    }
    // Handle other routes
    if (href !== '/dashboard' && pathname.startsWith(href)) {
      return true;
    }
    return false;
  };

  return (
    <div className={cn(
      'w-64 bg-white/95 backdrop-blur-xl border-r border-gray-200/60 h-full',
      'shadow-xl lg:shadow-none',
      className
    )}>
      {/* Logo/Brand */}
      <div className="p-6 border-b border-gray-200/60 bg-gradient-to-r from-primary-600 to-primary-700">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg">
            <span className="text-white font-bold text-lg">RP</span>
          </div>
          <div>
            <span className="text-xl font-bold text-white tracking-tight">RekruitPro</span>
            <div className="text-primary-100 text-sm font-medium">AI Platform</div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="p-4 space-y-2">
        {items.map((item) => {
          const active = isActive(item.href);
          return (
            <div key={item.id}>
              <button
                onClick={() => handleItemClick(item)}
                className={cn(
                  'w-full flex items-center justify-between px-4 py-3 text-sm font-semibold rounded-xl transition-all duration-200 ease-in-out group',
                  'hover:scale-[1.02] active:scale-[0.98]',
                  active
                    ? 'bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-lg shadow-primary-500/25'
                    : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                )}
                aria-current={active ? 'page' : undefined}
              >
                <div className="flex items-center space-x-3">
                  <Icon
                    name={item.icon}
                    size="sm"
                    className={cn(
                      'transition-colors duration-200',
                      active ? 'text-white' : 'text-gray-500 group-hover:text-gray-700'
                    )}
                  />
                  <span className="truncate">{item.label}</span>
                </div>
                {item.badge && (
                  <span className={cn(
                    'text-xs font-bold px-2 py-1 rounded-full transition-colors duration-200',
                    active
                      ? 'bg-white/20 text-white'
                      : 'bg-error-100 text-error-800 group-hover:bg-error-200'
                  )}>
                    {item.badge}
                  </span>
                )}
              </button>

              {/* Subtle description for better UX */}
              {item.description && !active && (
                <p className="text-xs text-gray-500 mt-1 ml-11 px-1 leading-relaxed">
                  {item.description}
                </p>
              )}
            </div>
          );
        })}
      </nav>

      {/* User Profile Section */}
      {/* <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
            <span className="text-gray-600 text-sm font-medium">JD</span>
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 truncate">John Doe</p>
            <p className="text-xs text-gray-500 truncate">HR Manager</p>
          </div>
          <button className="text-gray-400 hover:text-gray-600">
            <Icon name="settings" size="sm" />
          </button>
        </div>
      </div> */}
    </div>
  );
};

export default Sidebar;
