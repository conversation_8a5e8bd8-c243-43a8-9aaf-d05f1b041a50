import React from 'react';
import { cn } from '@/lib/utils';
import { Card, Icon } from '@/components/ui';
import { DashboardMetric } from '@/types';

interface MetricCardProps {
  metric: DashboardMetric;
  className?: string;
}

const MetricCard: React.FC<MetricCardProps> = ({ metric, className }) => {
  const getChangeColor = () => {
    if (!metric.change) return '';
    return metric.changeType === 'increase' ? 'text-success-600' : 'text-error-600';
  };

  const getChangeIcon = () => {
    if (!metric.change) return null;
    return metric.changeType === 'increase' ? (
      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L10 4.414 4.707 9.707a1 1 0 01-1.414 0z" clipRule="evenodd" />
      </svg>
    ) : (
      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M16.707 10.293a1 1 0 010 1.414l-6 6a1 1 0 01-1.414 0l-6-6a1 1 0 111.414-1.414L10 15.586l5.293-5.293a1 1 0 011.414 0z" clipRule="evenodd" />
      </svg>
    );
  };

  const getGradientColors = () => {
    const gradients = {
      blue: 'from-primary-500 to-primary-600',
      green: 'from-success-500 to-success-600',
      orange: 'from-warning-500 to-warning-600',
      red: 'from-error-500 to-error-600',
      purple: 'from-purple-500 to-purple-600',
    };
    return metric.color ? gradients[metric.color] : gradients.blue;
  };

  const getIconBgColors = () => {
    const colors = {
      blue: 'bg-primary-100 text-primary-600',
      green: 'bg-success-100 text-success-600',
      orange: 'bg-warning-100 text-warning-600',
      red: 'bg-error-100 text-error-600',
      purple: 'bg-purple-100 text-purple-600',
    };
    return metric.color ? colors[metric.color] : colors.blue;
  };

  return (
    <Card
      className={cn(
        'relative overflow-hidden group cursor-pointer',
        'hover:shadow-xl hover:-translate-y-1 transition-all duration-300 ease-out',
        'border-0 shadow-lg',
        className
      )}
      padding="lg"
    >
      {/* Gradient accent bar */}
      <div className={cn(
        'absolute top-0 left-0 right-0 h-1 bg-gradient-to-r',
        getGradientColors()
      )} />

      {/* Background pattern */}
      <div className="absolute top-0 right-0 w-32 h-32 opacity-5">
        <div className={cn(
          'w-full h-full rounded-full bg-gradient-to-br',
          getGradientColors(),
          'transform translate-x-8 -translate-y-8'
        )} />
      </div>

      <div className="relative flex items-start justify-between">
        <div className="flex-1 min-w-0">
          <p className="text-sm font-semibold text-gray-600 mb-2 uppercase tracking-wide">
            {metric.title}
          </p>
          <p className="text-4xl font-bold text-gray-900 mb-3 tracking-tight">
            {metric.value}
          </p>

          {metric.change && (
            <div className={cn(
              'flex items-center gap-2 text-sm font-medium',
              getChangeColor()
            )}>
              {getChangeIcon()}
              <span>{Math.abs(metric.change)}%</span>
              <span className="text-gray-500 font-normal">vs last month</span>
            </div>
          )}
        </div>

        {metric.icon && (
          <div className={cn(
            'p-4 rounded-2xl shadow-lg group-hover:scale-110 transition-transform duration-300',
            getIconBgColors()
          )}>
            <Icon name={metric.icon} size="lg" />
          </div>
        )}
      </div>

      {/* Subtle shine effect on hover */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out" />
    </Card>
  );
};

export default MetricCard;
