import React from 'react';
import { cn } from '@/lib/utils';
import { CardProps } from '@/types';

interface CardComponent extends React.FC<CardProps> {
  Header: React.FC<{ children: React.ReactNode; className?: string }>;
  Body: React.FC<{ children: React.ReactNode; className?: string }>;
  Footer: React.FC<{ children: React.ReactNode; className?: string }>;
}

const Card: CardComponent = ({
  children,
  title,
  subtitle,
  padding = 'md',
  shadow = 'sm',
  className,
  ...props
}) => {
  const baseStyles = `
    bg-white rounded-xl border border-gray-200/60
    transition-all duration-200 ease-in-out
    hover:shadow-lg hover:border-gray-300/60
    group relative overflow-hidden
  `;

  const paddings = {
    none: '',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
    xl: 'p-10',
  };

  const shadows = {
    none: '',
    sm: 'shadow-sm',
    md: 'shadow-md hover:shadow-lg',
    lg: 'shadow-lg hover:shadow-xl',
    xl: 'shadow-xl hover:shadow-2xl',
  };

  return (
    <div
      className={cn(
        baseStyles,
        paddings[padding],
        shadows[shadow],
        className
      )}
      {...props}
    >
      {/* Subtle gradient overlay for depth */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/50 to-transparent pointer-events-none" />

      {(title || subtitle) && (
        <div className="relative mb-6">
          {title && (
            <h3 className="text-xl font-bold text-gray-900 tracking-tight">
              {title}
            </h3>
          )}
          {subtitle && (
            <p className="text-sm text-gray-600 mt-2 leading-relaxed">
              {subtitle}
            </p>
          )}
        </div>
      )}
      <div className="relative">
        {children}
      </div>
    </div>
  );
};

// Card sub-components for better composition
const CardHeader: React.FC<{ children: React.ReactNode; className?: string }> = ({
  children,
  className,
}) => (
  <div className={cn(
    'relative mb-6 pb-4 border-b border-gray-200/60',
    'after:absolute after:bottom-0 after:left-0 after:right-0 after:h-px',
    'after:bg-gradient-to-r after:from-transparent after:via-gray-300 after:to-transparent',
    className
  )}>
    {children}
  </div>
);

const CardBody: React.FC<{ children: React.ReactNode; className?: string }> = ({
  children,
  className,
}) => (
  <div className={cn('relative flex-1', className)}>
    {children}
  </div>
);

const CardFooter: React.FC<{ children: React.ReactNode; className?: string }> = ({
  children,
  className,
}) => (
  <div className={cn(
    'relative mt-6 pt-4 border-t border-gray-200/60',
    'before:absolute before:top-0 before:left-0 before:right-0 before:h-px',
    'before:bg-gradient-to-r before:from-transparent before:via-gray-300 before:to-transparent',
    className
  )}>
    {children}
  </div>
);

Card.Header = CardHeader;
Card.Body = CardBody;
Card.Footer = CardFooter;

export default Card as CardComponent;
