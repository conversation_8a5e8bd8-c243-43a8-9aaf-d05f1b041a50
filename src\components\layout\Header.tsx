import React from 'react';
import { cn } from '@/lib/utils';
import { Button, Icon, Input } from '@/components/ui';

interface HeaderProps {
  title?: string;
  showSearch?: boolean;
  onSearch?: (query: string) => void;
  actions?: React.ReactNode;
  className?: string;
  onMenuClick?: () => void;
  showMenuButton?: boolean;
}

const Header: React.FC<HeaderProps> = ({
  title,
  showSearch = true,
  onSearch,
  actions,
  className,
  onMenuClick,
  showMenuButton = false,
}) => {
  const [searchQuery, setSearchQuery] = React.useState('');

  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    if (onSearch) {
      onSearch(value);
    }
  };

  return (
    <header className={cn(
      'bg-white/95 backdrop-blur-xl border-b border-gray-200/60 px-4 sm:px-6 py-4',
      'shadow-sm',
      className
    )}>
      <div className="flex items-center justify-between">
        {/* Left side - Menu <PERSON>, Title and Search */}
        <div className="flex items-center space-x-3 sm:space-x-6">
          {/* Mobile Menu Button */}
          {showMenuButton && (
            <button
              onClick={onMenuClick}
              className={cn(
                'lg:hidden p-2.5 rounded-xl text-gray-600 hover:text-gray-900',
                'hover:bg-gray-100 focus:outline-none focus-visible:ring-2 focus-visible:ring-primary-500',
                'transition-all duration-200 ease-in-out active:scale-95'
              )}
              aria-label="Open sidebar"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          )}

          {/* Page Title */}
          <div className="flex items-center space-x-3">
            {title && (
              <h1 className="text-xl sm:text-2xl font-bold text-gray-900 tracking-tight truncate">
                {title}
              </h1>
            )}
            {/* Breadcrumb indicator */}
            <div className="hidden sm:flex items-center text-sm text-gray-500">
              <svg className="w-4 h-4 mx-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
              <span className="text-primary-600 font-medium">Dashboard</span>
            </div>
          </div>
        </div>

        {/* Right side - Actions and User Menu */}
        <div className="flex items-center space-x-3">
          {actions}

          {/* Notifications */}
          <button className={cn(
            'relative p-2.5 rounded-xl text-gray-600 hover:text-gray-900 hover:bg-gray-100',
            'focus:outline-none focus-visible:ring-2 focus-visible:ring-primary-500',
            'transition-all duration-200 ease-in-out active:scale-95'
          )}>
            <Icon name="bell" size="md" />
            <span className="absolute top-1 right-1 w-2 h-2 bg-error-500 rounded-full animate-pulse"></span>
          </button>

          {/* User Menu */}
          <div className="relative">
            <button className={cn(
              'flex items-center space-x-2 p-2 rounded-xl hover:bg-gray-100',
              'focus:outline-none focus-visible:ring-2 focus-visible:ring-primary-500',
              'transition-all duration-200 ease-in-out'
            )}>
              <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center shadow-lg">
                <span className="text-white text-sm font-bold">JD</span>
              </div>
              <Icon name="chevronDown" size="sm" className="text-gray-400" />
            </button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
