import React from 'react';
import { cn } from '@/lib/utils';
import { Button, Icon, Input } from '@/components/ui';

interface HeaderProps {
  title?: string;
  showSearch?: boolean;
  onSearch?: (query: string) => void;
  actions?: React.ReactNode;
  className?: string;
  onMenuClick?: () => void;
  showMenuButton?: boolean;
}

const Header: React.FC<HeaderProps> = ({
  title,
  showSearch = true,
  onSearch,
  actions,
  className,
  onMenuClick,
  showMenuButton = false,
}) => {
  const [searchQuery, setSearchQuery] = React.useState('');

  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    if (onSearch) {
      onSearch(value);
    }
  };

  return (
    <header className={cn('bg-white border-b border-gray-200 px-4 sm:px-6 py-4', className)}>
      <div className="flex items-center justify-between">
        {/* Left side - Menu <PERSON>, Title and Search */}
        <div className="flex items-center space-x-3 sm:space-x-6">
          {/* Mobile Menu Button */}
          {showMenuButton && (
            <button
              onClick={onMenuClick}
              className="lg:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
              aria-label="Open sidebar"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          )}

          {title && (
            <h1 className="text-xl sm:text-2xl font-bold text-gray-900 truncate">{title}</h1>
          )}
          
          {/* {showSearch && (
            <div className="relative hidden sm:block">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Icon name="search" size="sm" className="text-gray-400" />
              </div>
              <Input
                type="search"
                placeholder="Search candidates, jobs..."
                value={searchQuery}
                onChange={handleSearchChange}
                className="pl-10 w-64 lg:w-80"
              />
            </div>
          )} */}
        </div>

        {/* Right side - Actions and User Menu */}
        <div className="flex items-center space-x-4">
          {actions}
          
          {/* Notifications */}
          <button className="relative p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100">
            <Icon name="bell" size="md" />
            <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
          </button>

          {/* User Menu */}
          <div className="relative">
            <button className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100">
              <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">JD</span>
              </div>
              <Icon name="chevronDown" size="sm" className="text-gray-400" />
            </button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
