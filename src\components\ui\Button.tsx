import React from 'react';
import { cn } from '@/lib/utils';
import { ButtonProps } from '@/types';

const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  onClick,
  type = 'button',
  className,
  ...props
}) => {
  const baseStyles = `
    inline-flex items-center justify-center font-semibold transition-all duration-200 ease-in-out
    focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-offset-white
    disabled:opacity-50 disabled:cursor-not-allowed disabled:pointer-events-none
    active:scale-[0.98] transform-gpu
    relative overflow-hidden
  `;

  const variants = {
    primary: `
      bg-gradient-to-r from-primary-600 to-primary-700 text-white shadow-lg
      hover:from-primary-700 hover:to-primary-800 hover:shadow-xl
      focus-visible:ring-primary-500
      border border-primary-600
    `,
    secondary: `
      bg-white text-secondary-700 border border-secondary-300 shadow-sm
      hover:bg-secondary-50 hover:border-secondary-400 hover:shadow-md
      focus-visible:ring-secondary-500
    `,
    outline: `
      bg-transparent text-primary-700 border-2 border-primary-600
      hover:bg-primary-50 hover:border-primary-700
      focus-visible:ring-primary-500
    `,
    ghost: `
      bg-transparent text-secondary-700 border border-transparent
      hover:bg-secondary-100 hover:text-secondary-900
      focus-visible:ring-secondary-500
    `,
    danger: `
      bg-gradient-to-r from-error-600 to-error-700 text-white shadow-lg
      hover:from-error-700 hover:to-error-800 hover:shadow-xl
      focus-visible:ring-error-500
      border border-error-600
    `,
    warning: `
      bg-gradient-to-r from-warning-500 to-warning-600 text-white shadow-lg
      hover:from-warning-600 hover:to-warning-700 hover:shadow-xl
      focus-visible:ring-warning-500
      border border-warning-500
    `,
    success: `
      bg-gradient-to-r from-success-600 to-success-700 text-white shadow-lg
      hover:from-success-700 hover:to-success-800 hover:shadow-xl
      focus-visible:ring-success-500
      border border-success-600
    `,
  };

  const sizes = {
    xs: 'px-2.5 py-1.5 text-xs rounded-md gap-1',
    sm: 'px-3 py-2 text-sm rounded-lg gap-1.5',
    md: 'px-4 py-2.5 text-sm rounded-lg gap-2',
    lg: 'px-6 py-3 text-base rounded-xl gap-2',
    xl: 'px-8 py-4 text-lg rounded-xl gap-3',
  };

  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (disabled || loading) return;

    // Add ripple effect
    const button = e.currentTarget;
    const rect = button.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = e.clientX - rect.left - size / 2;
    const y = e.clientY - rect.top - size / 2;

    const ripple = document.createElement('span');
    ripple.style.cssText = `
      position: absolute;
      width: ${size}px;
      height: ${size}px;
      left: ${x}px;
      top: ${y}px;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      transform: scale(0);
      animation: ripple 0.6s linear;
      pointer-events: none;
    `;

    button.appendChild(ripple);
    setTimeout(() => ripple.remove(), 600);

    onClick?.(e);
  };

  return (
    <button
      type={type}
      onClick={handleClick}
      disabled={disabled || loading}
      className={cn(
        baseStyles,
        variants[variant],
        sizes[size],
        className
      )}
      aria-disabled={disabled || loading}
      {...props}
    >
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center bg-inherit rounded-inherit">
          <svg
            className="animate-spin h-4 w-4 text-current"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            aria-hidden="true"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
        </div>
      )}
      <span className={cn('flex items-center gap-inherit', loading && 'opacity-0')}>
        {children}
      </span>
    </button>
  );
};

export default Button;
