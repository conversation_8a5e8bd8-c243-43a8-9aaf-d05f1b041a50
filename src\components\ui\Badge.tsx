import React from 'react';
import { cn } from '@/lib/utils';
import { BadgeProps } from '@/types';

const Badge: React.FC<BadgeProps> = ({
  children,
  variant = 'default',
  size = 'md',
  className,
  ...props
}) => {
  const baseStyles = `
    inline-flex items-center font-semibold rounded-full
    transition-all duration-200 ease-in-out
    border shadow-sm
  `;

  const variants = {
    default: `
      bg-gray-100 text-gray-700 border-gray-200
      hover:bg-gray-200 hover:border-gray-300
    `,
    success: `
      bg-success-100 text-success-800 border-success-200
      hover:bg-success-200 hover:border-success-300
    `,
    warning: `
      bg-warning-100 text-warning-800 border-warning-200
      hover:bg-warning-200 hover:border-warning-300
    `,
    danger: `
      bg-error-100 text-error-800 border-error-200
      hover:bg-error-200 hover:border-error-300
    `,
    info: `
      bg-info-100 text-info-800 border-info-200
      hover:bg-info-200 hover:border-info-300
    `,
    primary: `
      bg-primary-100 text-primary-800 border-primary-200
      hover:bg-primary-200 hover:border-primary-300
    `,
    secondary: `
      bg-secondary-100 text-secondary-800 border-secondary-200
      hover:bg-secondary-200 hover:border-secondary-300
    `,
  };

  const sizes = {
    xs: 'px-2 py-0.5 text-xs gap-1',
    sm: 'px-2.5 py-1 text-xs gap-1',
    md: 'px-3 py-1 text-sm gap-1.5',
    lg: 'px-3.5 py-1.5 text-sm gap-1.5',
    xl: 'px-4 py-2 text-base gap-2',
  };

  return (
    <span
      className={cn(
        baseStyles,
        variants[variant],
        sizes[size],
        className
      )}
      {...props}
    >
      {children}
    </span>
  );
};

export default Badge;
